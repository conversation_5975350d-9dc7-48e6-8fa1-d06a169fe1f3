group 'com.example.asl_flutter_input'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.example.asl_flutter_input'
    }

    compileSdk 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
    }

    dependencies {
        testImplementation 'org.jetbrains.kotlin:kotlin-test'
        testImplementation 'org.mockito:mockito-core:5.0.0'



       // AAR file moved to example app to avoid AAR-in-AAR build issues
       // implementation fileTree(dir: 'libs', include: ['*.aar'])
       // implementation files('libs/asl_v1.0.5.aar')

        implementation 'androidx.multidex:multidex:2.0.1'
        implementation 'com.android.support:appcompat-v7:28.0.0'
        implementation 'com.android.support.constraint:constraint-layout:2.0.4'

        // Use compileOnly for dependencies to avoid AAR-in-AAR build issues
        // The app using this plugin must include these as implementation dependencies

        // Google Cloud Speech API dependencies - using older stable versions
        compileOnly 'com.google.api.grpc:grpc-google-cloud-speech-v1p1beta1:0.63.0'

        // Google API GAX libraries (contains ResponseObserver)
        compileOnly 'com.google.api:gax:2.19.0'
        compileOnly 'com.google.api:gax-grpc:2.19.0'

        // Google API common libraries
        compileOnly 'com.google.api:api-common:2.2.1'
        compileOnly 'com.google.auth:google-auth-library-credentials:1.11.0'
        compileOnly 'com.google.auth:google-auth-library-oauth2-http:1.11.0'

        // Protocol Buffers
        compileOnly 'com.google.protobuf:protobuf-java:3.22.3'
        compileOnly 'com.google.protobuf:protobuf-java-util:3.22.3'
        compileOnly 'com.google.api.grpc:proto-google-common-protos:2.9.0'

        // gRPC dependencies
        compileOnly 'io.grpc:grpc-okhttp:1.32.2'
        compileOnly 'io.grpc:grpc-protobuf:1.32.2'
        compileOnly 'io.grpc:grpc-stub:1.32.2'
        compileOnly 'io.grpc:grpc-auth:1.32.2'
        compileOnly 'io.grpc:grpc-netty-shaded:1.32.2'

        // Logging
        compileOnly 'com.google.flogger:flogger:0.4'
        compileOnly 'com.google.flogger:flogger-system-backend:0.4'

        // Other dependencies
        compileOnly 'joda-time:joda-time:2.9.2'

        // AAR file classes (compileOnly to avoid packaging issues)
        compileOnly fileTree(dir: '../example/android/app/libs', include: ['*.aar'])
        implementation('androidx.core:core-ktx:1.3.2')
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
