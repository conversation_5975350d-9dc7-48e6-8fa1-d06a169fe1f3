group 'com.example.asl_flutter_input'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.example.asl_flutter_input'
    }

    compileSdk 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
    }

    dependencies {
        testImplementation 'org.jetbrains.kotlin:kotlin-test'
        testImplementation 'org.mockito:mockito-core:5.0.0'



       compileOnly fileTree(dir: 'libs', include: ['*.aar'])
//       implementation files('libs/asl_v1.0.0.aar')

        implementation 'androidx.multidex:multidex:2.0.1'
        implementation 'com.android.support:appcompat-v7:28.0.0'
        implementation 'com.android.support.constraint:constraint-layout:2.0.4'
        implementation 'com.google.api.grpc:grpc-google-cloud-speech-v1p1beta1:0.63.0'
        implementation 'com.google.flogger:flogger:0.4'
        implementation 'com.google.flogger:flogger-system-backend:0.4'
        implementation 'com.google.protobuf:protobuf-java:3.22.3'
        implementation 'com.google.protobuf:protobuf-java-util:3.22.3'
        implementation 'joda-time:joda-time:2.9.2'
        implementation 'io.grpc:grpc-okhttp:1.32.2'
        implementation('androidx.core:core-ktx:1.3.2')
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
