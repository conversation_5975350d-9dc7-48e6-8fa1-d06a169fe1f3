import 'dart:async';

import 'package:asl_flutter_input/asl_flutter_input.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class AudioTranscriptionScreen extends StatefulWidget {
  const AudioTranscriptionScreen({super.key});

  @override
  State<AudioTranscriptionScreen> createState() =>
      _AudioTranscriptionScreenState();
}

class _AudioTranscriptionScreenState extends State<AudioTranscriptionScreen> {
  bool _isListening = false;
  bool isDefaultLanguage = true; //set to fasle for arabic

  String _recognizedText = "";
  String _listenStatus = "Press the button and start speaking";
  String _currentLanguage = "en-US";

  Timer? _timer;
  final _aslFlutterInputPlugin = AslFlutterInput();

  PermissionStatus status = PermissionStatus.denied;

  @override
  void initState() {
    super.initState();
    _reqAudioPermission();
  }

  @override
  void dispose() {
    // Cancel the timer when the widget is disposed
    _timer?.cancel();
    super.dispose();
  }

  _reqAudioPermission() async {
    // Request microphone permission
    status = await Permission.microphone.request();

    if (status.isGranted) {
      // Permission is granted, you can start recording audio
      print('Microphone permission granted.');
    } else if (status.isDenied) {
      // Permission is denied, handle accordingly
      print('Microphone permission denied.');
    } else if (status.isPermanentlyDenied) {
      // Permission is permanently denied, open app settings
      print(
          'Microphone permission permanently denied. Please enable it from settings.');
      openAppSettings();
    }
    setState(() {});
  }

  _setAPIKey() async {
    await _aslFlutterInputPlugin
        .setAPIKey("AIzaSyCizaPQJGhorO8m00L3uBuJzX3H5hVm_2c");
  }

  _handleLanguageChangeCallBack() async {
    setState(() {
      isDefaultLanguage = !isDefaultLanguage;
    });

    _currentLanguage = await _aslFlutterInputPlugin.setCurrentLanguage(
        isDefaultLanguage
            ? 'en-US'
            : 'ar-SA'); // by default , current language will be 'en-US'
    setState(() {});
  }

  void _startListening() async {
    try {
      await _setAPIKey();
      await _aslFlutterInputPlugin.initAudioTranscription("", "");

      setState(() {
        _isListening = true;
        _listenStatus = 'Listening...';
      });

      await _startTimer();
    } on Exception catch (e) {
      debugPrint('[Exception][_startListening]: $e');
    }
  }

  _startTimer() async {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) async {
      // Your repeated method call here
      _recognizedText = await _aslFlutterInputPlugin.fetchTranscribedText();

      setState(() {});
    });
  }

  _stopListening() async {
    setState(() {
      _isListening = false;
      _listenStatus = 'Press the button and start speaking';
      // _recognizedText = '';
    });
    await _aslFlutterInputPlugin.stopListening();
    _timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Audio Live Transcrption'),
        ),
        body: Center(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child: SingleChildScrollView(
                        child: Text(
                  _recognizedText,
                  style: const TextStyle(fontSize: 16),
                ))),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                        onPressed: () => status.isGranted
                            ? !_isListening
                                ? _startListening()
                                : _stopListening()
                            : null,
                        icon: Icon(
                          _isListening ? Icons.mic_outlined : Icons.mic_off,
                          size: 40,
                        )),
                    SizedBox(width: status.isGranted && !_isListening ? 20 : 0),
                    Visibility(
                      visible: status.isGranted && !_isListening,
                      child: IconButton(
                          onPressed: () => _handleLanguageChangeCallBack(),
                          icon: const Icon(
                            Icons.translate,
                            size: 40,
                          )),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Text('Current Language: $_currentLanguage'),
                const SizedBox(height: 10),
                Text(_listenStatus),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
