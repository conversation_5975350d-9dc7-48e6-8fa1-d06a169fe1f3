import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'asl_flutter_input_method_channel.dart';

abstract class AslFlutterInputPlatform extends PlatformInterface {
  /// Constructs a AslFlutterInputPlatform.
  AslFlutterInputPlatform() : super(token: _token);

  static final Object _token = Object();

  static AslFlutterInputPlatform _instance = MethodChannelAslFlutterInput();

  /// The default instance of [AslFlutterInputPlatform] to use.
  ///
  /// Defaults to [MethodChannelAslFlutterInput].
  static AslFlutterInputPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [AslFlutterInputPlatform] when
  /// they register themselves.
  static set instance(AslFlutterInputPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<String> setCurrentLanguage(String languageCode) {
    throw UnimplementedError('setCurrentLanguage() has not been implemented.');
  }

  Future<String> setAPIKey(String apiKey) async {
    throw UnimplementedError('setAPIKey() has not been implemented.');
  }

  initAudioTranscription(String projectId, String serviceAccountJson) {
    throw UnimplementedError(
        'initAudioTranscription() has not been implemented.');
  }

  Future<String> fetchTranscribedText() {
    throw UnimplementedError(
        'fetchTranscribedText() has not been implemented.');
  }

  stopListening() {
    throw UnimplementedError('stopListening() has not been implemented.');
  }
}
