import 'asl_flutter_input_platform_interface.dart';

class AslFlutterInput {
  Future<String> setCurrentLanguage(String languageCode) {
    return AslFlutterInputPlatform.instance.setCurrentLanguage(languageCode);
  }

  Future<String> setAPIKey(String apiKey) async {
    return AslFlutterInputPlatform.instance.setAPIKey(apiKey);
  }

  initAudioTranscription(String projectId, String serviceAccountJson) {
    return AslFlutterInputPlatform.instance
        .initAudioTranscription(projectId, serviceAccountJson);
  }

  Future<String> fetchTranscribedText() {
    return AslFlutterInputPlatform.instance.fetchTranscribedText();
  }

  stopListening() {
    return AslFlutterInputPlatform.instance.stopListening();
  }
}
